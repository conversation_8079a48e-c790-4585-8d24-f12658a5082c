package util

import (
	"errors"
	"os"
	"path/filepath"
	"reflect"
	"sort"
	"testing"
)

func TestReadDirNames(t *testing.T) {
	t.Run("Table-driven ReadDirNames tests", func(t *testing.T) {
		base := t.TempDir()

		dirWithFiles := filepath.Join(base, "with_files")
		os.Mkdir(dirWithFiles, 0o755)
		os.WriteFile(filepath.Join(dirWithFiles, "a.txt"), []byte("a"), 0o644)
		os.WriteFile(filepath.Join(dirWithFiles, "b.txt"), []byte("b"), 0o644)

		emptyDir := filepath.Join(base, "empty")
		os.Mkdir(emptyDir, 0o755)

		missingDir := filepath.Join(base, "missing")

		tests := []struct {
			name      string
			input     string
			want      []string
			expectErr error
		}{
			{
				name:      "Valid directory with files",
				input:     dirWithFiles,
				want:      []string{"a.txt", "b.txt"},
				expectErr: nil,
			},
			{
				name:      "Empty directory",
				input:     emptyDir,
				want:      nil,
				expectErr: errors.New("no entries found"),
			},
			{
				name:      "Non-existent directory",
				input:     missingDir,
				want:      nil,
				expectErr: os.ErrNotExist,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				got, err := ReadDirNames(tt.input, false) // Test without showing hidden files

				if tt.expectErr != nil {
					if err == nil {
						t.Errorf("expected error %v, got nil", tt.expectErr)
						return
					}

					if errors.Is(tt.expectErr, os.ErrNotExist) {
						if !os.IsNotExist(err) {
							t.Errorf("expected a file-not-exist error, got: %v", err)
						}
						return
					}

					if err.Error() != tt.expectErr.Error() {
						t.Errorf("expected error %q, got %q", tt.expectErr.Error(), err.Error())
					}
					return
				}

				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				sort.Strings(got)
				sort.Strings(tt.want)

				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("expected %v, got %v", tt.want, got)
				}
			})
		}
	})
}

func TestReadDirNamesWithHiddenFiles(t *testing.T) {
	t.Run("Test hidden files functionality", func(t *testing.T) {
		base := t.TempDir()

		// Create a directory with both regular and hidden files
		testDir := filepath.Join(base, "test_hidden")
		os.Mkdir(testDir, 0o755)

		// Create regular files
		os.WriteFile(filepath.Join(testDir, "visible.txt"), []byte("visible"), 0o644)
		os.WriteFile(filepath.Join(testDir, "another.txt"), []byte("another"), 0o644)

		// Create hidden files
		os.WriteFile(filepath.Join(testDir, ".hidden"), []byte("hidden"), 0o644)
		os.WriteFile(filepath.Join(testDir, ".gitignore"), []byte("gitignore"), 0o644)

		tests := []struct {
			name       string
			showHidden bool
			want       []string
		}{
			{
				name:       "Without -a flag (hide hidden files)",
				showHidden: false,
				want:       []string{"visible.txt", "another.txt"},
			},
			{
				name:       "With -a flag (show hidden files)",
				showHidden: true,
				want:       []string{"visible.txt", "another.txt", ".hidden", ".gitignore"},
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				got, err := ReadDirNames(testDir, tt.showHidden)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				sort.Strings(got)
				sort.Strings(tt.want)

				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("expected %v, got %v", tt.want, got)
				}
			})
		}
	})
}
