package util

import (
	"errors"
	"os"
)

// ReadDirNames returns a list of file and directory names in dirPath
func ReadDirNames(dirPath string) ([]string, error) {
	dir, err := os.Open(dirPath)
	if err != nil {
		return nil, err
	}
	defer dir.Close()

	entries, err := dir.Readdir(-1)
	if err != nil {
		return nil, err
	}

	var names []string
	for _, entry := range entries {
		names = append(names, entry.Name())
	}
	if names == nil {
		return nil, errors.New("no entries found")
	}
	return names, nil
}
